from odoo import models


class AccountMove(models.Model):
    _inherit = 'account.move'

    def _get_invoice_lines_by_type(self):
        """
        Group invoice lines by product type for invoice reports.
        Returns a dictionary with product types as keys and line lists as values.
        """
        lines_by_type = {
            'cigars': [],
            'cigarettes': [],
            'tobacco': [],
            'others': []
        }
        
        for line in self.invoice_line_ids:
            if line.product_id:
                if line.product_id.is_cigar:
                    lines_by_type['cigars'].append(line)
                elif line.product_id.is_cigarette:
                    lines_by_type['cigarettes'].append(line)
                elif line.product_id.is_tobacco:
                    lines_by_type['tobacco'].append(line)
                else:
                    lines_by_type['others'].append(line)
            else:
                lines_by_type['others'].append(line)
        
        return lines_by_type
