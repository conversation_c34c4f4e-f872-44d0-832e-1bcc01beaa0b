<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- Enhanced Invoice Report with Product Type Grouping -->
        <template id="report_invoice_document_enhanced" inherit_id="account.report_invoice_document">
            <xpath expr="//table[@name='invoice_line_table']/tbody" position="replace">
                <tbody class="invoice_tbody">
                        <!-- CIGARS Section -->
                        <t t-set="cigar_lines" t-value="o.invoice_line_ids.filtered(lambda line: line.product_id and line.product_id.is_cigar)"/>
                        <t t-if="cigar_lines">
                            <tr class="bg-200 font-weight-bold">
                                <td colspan="5" style="border-top: 2px solid #dee2e6; padding: 8px;">
                                    <strong>CIGARS</strong>
                                    <span class="float-right">
                                        <t t-set="cigar_total" t-value="sum(cigar_lines.mapped('price_subtotal')) if cigar_lines else 0"/>
                                        <span t-esc="cigar_total" t-options='{"widget": "monetary", "display_currency": o.currency_id}'/>
                                    </span>
                                </td>
                            </tr>
                            <t t-foreach="cigar_lines" t-as="line">
                                <tr t-att-class="'bg-200 font-weight-bold' if line_index == 0 else ''">
                                    <td name="account_invoice_line_name"><span t-field="line.name" t-options="{'widget': 'text'}"/></td>
                                    <td class="text-right">
                                        <span t-field="line.quantity"/>
                                        <span t-field="line.product_uom_id" groups="uom.group_uom"/>
                                    </td>
                                    <td class="text-right">
                                        <span t-field="line.price_unit"/>
                                    </td>
                                    <td class="text-right" groups="account.group_show_line_subtotals_tax_excluded">
                                        <span t-field="line.price_subtotal" t-options='{"widget": "monetary", "display_currency": o.currency_id}'/>
                                    </td>
                                    <td class="text-right" groups="account.group_show_line_subtotals_tax_included">
                                        <span t-field="line.price_total" t-options='{"widget": "monetary", "display_currency": o.currency_id}'/>
                                    </td>
                                </tr>
                            </t>
                        </t>

                        <!-- CIGARETTES Section (Temporarily Commented) -->

                        <t t-set="cigarette_lines" t-value="o.invoice_line_ids.filtered(lambda line: line.product_id and line.product_id.is_cigarette)"/>
                        <t t-if="cigarette_lines">
                            <tr class="bg-200 font-weight-bold">
                                <td colspan="5" style="border-top: 2px solid #dee2e6; padding: 8px;">
                                    <strong>CIGARETTES</strong>
                                    <span class="float-right">
                                        <t t-set="cigarette_total" t-value="sum(cigarette_lines.mapped('price_subtotal')) if cigarette_lines else 0"/>
                                        <span t-esc="cigarette_total" t-options='{"widget": "monetary", "display_currency": o.currency_id}'/>
                                    </span>
                                </td>
                            </tr>
                            <t t-foreach="cigarette_lines" t-as="line">
                                <tr t-att-class="'bg-200 font-weight-bold' if line_index == 0 else ''">
                                    <td name="account_invoice_line_name"><span t-field="line.name" t-options="{'widget': 'text'}"/></td>
                                    <td class="text-right">
                                        <span t-field="line.quantity"/>
                                        <span t-field="line.product_uom_id" groups="uom.group_uom"/>
                                    </td>
                                    <td class="text-right">
                                        <span t-field="line.price_unit"/>
                                    </td>
                                    <td class="text-right" groups="account.group_show_line_subtotals_tax_excluded">
                                        <span t-field="line.price_subtotal" t-options='{"widget": "monetary", "display_currency": o.currency_id}'/>
                                    </td>
                                    <td class="text-right" groups="account.group_show_line_subtotals_tax_included">
                                        <span t-field="line.price_total" t-options='{"widget": "monetary", "display_currency": o.currency_id}'/>
                                    </td>
                                </tr>
                            </t>
                        </t>


                        <!-- TOBACCO Section -->
                        <t t-set="tobacco_lines" t-value="o.invoice_line_ids.filtered(lambda line: line.product_id and line.product_id.is_tobacco)"/>
                        <t t-if="tobacco_lines">
                            <tr class="bg-200 font-weight-bold">
                                <td colspan="5" style="border-top: 2px solid #dee2e6; padding: 8px;">
                                    <strong>TOBACCO PRODUCTS</strong>
                                    <span class="float-right">
                                        <t t-set="tobacco_total" t-value="sum(tobacco_lines.mapped('price_subtotal')) if tobacco_lines else 0"/>
                                        <span t-esc="tobacco_total" t-options='{"widget": "monetary", "display_currency": o.currency_id}'/>
                                    </span>
                                </td>
                            </tr>
                            <t t-foreach="tobacco_lines" t-as="line">
                                <tr t-att-class="'bg-200 font-weight-bold' if line_index == 0 else ''">
                                    <td name="account_invoice_line_name"><span t-field="line.name" t-options="{'widget': 'text'}"/></td>
                                    <td class="text-right">
                                        <span t-field="line.quantity"/>
                                        <span t-field="line.product_uom_id" groups="uom.group_uom"/>
                                    </td>
                                    <td class="text-right">
                                        <span t-field="line.price_unit"/>
                                    </td>
                                    <td class="text-right" groups="account.group_show_line_subtotals_tax_excluded">
                                        <span t-field="line.price_subtotal" t-options='{"widget": "monetary", "display_currency": o.currency_id}'/>
                                    </td>
                                    <td class="text-right" groups="account.group_show_line_subtotals_tax_included">
                                        <span t-field="line.price_total" t-options='{"widget": "monetary", "display_currency": o.currency_id}'/>
                                    </td>
                                </tr>
                            </t>
                        </t>

                        <!-- OTHER PRODUCTS Section -->
                        <t t-set="other_lines" t-value="o.invoice_line_ids.filtered(lambda line: not line.product_id or (not line.product_id.is_cigar and not line.product_id.is_cigarette and not line.product_id.is_tobacco))"/>
                        <t t-if="other_lines">
                            <tr class="bg-200 font-weight-bold">
                                <td colspan="5" style="border-top: 2px solid #dee2e6; padding: 8px;">
                                    <strong>OTHER PRODUCTS</strong>
                                    <span class="float-right">
                                        <t t-set="other_total" t-value="sum(other_lines.mapped('price_subtotal')) if other_lines else 0"/>
                                        <span t-esc="other_total" t-options='{"widget": "monetary", "display_currency": o.currency_id}'/>
                                    </span>
                                </td>
                            </tr>
                            <t t-foreach="other_lines" t-as="line">
                                <tr t-att-class="'bg-200 font-weight-bold' if line_index == 0 else ''">
                                    <td name="account_invoice_line_name"><span t-field="line.name" t-options="{'widget': 'text'}"/></td>
                                    <td class="text-right">
                                        <span t-field="line.quantity"/>
                                        <span t-field="line.product_uom_id" groups="uom.group_uom"/>
                                    </td>
                                    <td class="text-right">
                                        <span t-field="line.price_unit"/>
                                    </td>
                                    <td class="text-right" groups="account.group_show_line_subtotals_tax_excluded">
                                        <span t-field="line.price_subtotal" t-options='{"widget": "monetary", "display_currency": o.currency_id}'/>
                                    </td>
                                    <td class="text-right" groups="account.group_show_line_subtotals_tax_included">
                                        <span t-field="line.price_total" t-options='{"widget": "monetary", "display_currency": o.currency_id}'/>
                                    </td>
                                </tr>
                            </t>
                        </t>
                </tbody>

                <!-- Product Type Summary Section -->
                <div class="clearfix" style="margin-top: 20px;">
                    <div class="row">
                        <div class="col-6">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Category</th>
                                        <th class="text-right">Quantity</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <t t-set="cigar_lines_summary" t-value="o.invoice_line_ids.filtered(lambda line: line.product_id and line.product_id.is_cigar)"/>
                                    <t t-set="cigar_qty" t-value="sum(cigar_lines_summary.mapped('quantity')) if cigar_lines_summary else 0"/>
                                    <t t-if="cigar_qty > 0">
                                        <tr>
                                            <td>CIGAR</td>
                                            <td class="text-right"><span t-esc="cigar_qty"/></td>
                                        </tr>
                                    </t>

                                    <t t-set="cigarette_lines_summary" t-value="o.invoice_line_ids.filtered(lambda line: line.product_id and line.product_id.is_cigarette)"/>
                                    <t t-set="cigarette_qty" t-value="sum(cigarette_lines_summary.mapped('quantity')) if cigarette_lines_summary else 0"/>
                                    <t t-if="cigarette_qty > 0">
                                        <tr>
                                            <td>CIGARETTE</td>
                                            <td class="text-right"><span t-esc="cigarette_qty"/></td>
                                        </tr>
                                    </t>

                                    <t t-set="tobacco_lines_summary" t-value="o.invoice_line_ids.filtered(lambda line: line.product_id and line.product_id.is_tobacco)"/>
                                    <t t-set="tobacco_qty" t-value="sum(tobacco_lines_summary.mapped('quantity')) if tobacco_lines_summary else 0"/>
                                    <t t-if="tobacco_qty > 0">
                                        <tr>
                                            <td>TOBACCO</td>
                                            <td class="text-right"><span t-esc="tobacco_qty"/></td>
                                        </tr>
                                    </t>

                                    <t t-set="other_lines_summary" t-value="o.invoice_line_ids.filtered(lambda line: not line.product_id or (not line.product_id.is_cigar and not line.product_id.is_cigarette and not line.product_id.is_tobacco))"/>
                                    <t t-set="other_qty" t-value="sum(other_lines_summary.mapped('quantity')) if other_lines_summary else 0"/>
                                    <t t-if="other_qty > 0">
                                        <tr>
                                            <td>DISPOSABLE</td>
                                            <td class="text-right"><span t-esc="other_qty"/></td>
                                        </tr>
                                    </t>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </xpath>
        </template>


    </data>
</odoo>
